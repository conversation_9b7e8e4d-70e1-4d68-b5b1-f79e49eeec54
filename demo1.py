# Demo1 项目 - 临时测试文件
# 创建时间: 2025年

def demo1_test_function():
    """Demo1项目的测试函数"""
    print("这是Demo1项目的测试函数")
    return "Demo1测试成功"

def greet(name):
    message = f"Hello, {name}!"
    print(message)
    return message

def add(a, b):
    result = a + b
    print(f"The result of {a} + {b} is {result}")
    return result

def main():
    name = "Alice"
    greet(name)

    x = 5
    y = 7
    total = add(x, y)
    print("Done!")

if __name__ == "__main__":
    result = demo1_test_function()
    print(f"执行结果: {result}")
